const axios = require("axios");
const crypto = require("crypto-js");
const { Transform } = require("stream");
const wav = require("node-wav");
const WebSocket = require("ws");
const { randomUUID } = require("crypto");

class DoubaoTtsService {
  constructor(config) {
    this.appId = config.APPID;
    this.accessToken = config.ACCESS_TOKEN;
    this.secretKey = config.SECRET_KEY;
    this.voiceType = config.VOICE_TYPE;
    this.ttsUrl = "https://openspeech.bytedance.com/api/v1/tts";

    // 双向流式TTS配置
    this.bidirectionalConfig = {
      url: "wss://openspeech.bytedance.com/api/v3/tts/bidirection",
      resourceId: "volc.service_type.10029", // 大模型语音合成及混音
    };

    // 音频配置
    this.audioConfig = {
      format: "mp3", // 默认MP3格式
      sample_rate: 24000,
      bit_depth: 16,
      channels: 1,
    };

    // 重试配置
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
    };

    // 分片配置
    this.chunkConfig = {
      maxTextLength: 1000, // 进一步增加最大文本长度
      chunkSize: 1024 * 8, // 音频分片大小 8KB
    };

    // 双向流式连接管理
    this.bidirectionalConnections = new Map(); // connectionId -> WebSocket connection info
    this.activeSessions = new Map(); // sessionId -> {connectionId, status, chunkIndex, timestamp, callback}
    this.ttsQueues = new Map(); // connectionId -> queue
    this.sessionCallbacks = new Map(); // sessionId -> callback function

    // 并发控制
    this.concurrencyConfig = {
      maxConcurrentSessions: 3, // 降低最大并发会话数
      maxConnectionsPerUser: 2, // 每个用户最大连接数
      sessionTimeout: 8000, // 会话超时时间 8秒
      cleanupInterval: 30000, // 清理间隔 30秒
      queueTimeout: 15000, // 队列等待超时 15秒
    };

    // 连接池管理
    this.connectionPools = new Map(); // connectionId -> connection pool
    this.sessionQueue = []; // 等待处理的会话队列
    this.processingQueue = false; // 队列处理状态

    // 音频缓冲管理
    this.audioBuffers = new Map(); // connectionId -> {chunks: Map(chunkIndex -> audioData[]), playOrder: []}

    // 启动定期清理
    this.startPeriodicCleanup();
  }

  /**
   * 启动定期清理
   */
  startPeriodicCleanup() {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, this.concurrencyConfig.cleanupInterval);
  }

  /**
   * 清理过期会话
   */
  cleanupExpiredSessions() {
    const now = Date.now();
    const expiredSessions = [];

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now - session.timestamp > this.concurrencyConfig.sessionTimeout) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      console.log(`🧹 [双向TTS] 清理过期会话: ${sessionId}`);
      this.activeSessions.delete(sessionId);
      this.sessionCallbacks.delete(sessionId);
    }

    if (expiredSessions.length > 0) {
      console.log(`🧹 [双向TTS] 清理了 ${expiredSessions.length} 个过期会话`);
    }
  }

  /**
   * 获取或创建音频缓冲区
   */
  getAudioBuffer(connectionId) {
    if (!this.audioBuffers.has(connectionId)) {
      this.audioBuffers.set(connectionId, {
        chunks: new Map(), // chunkIndex -> audioData[]
        playOrder: [], // 播放顺序
        lastPlayedIndex: -1,
      });
    }
    return this.audioBuffers.get(connectionId);
  }

  /**
   * 添加音频到缓冲区
   */
  addAudioToBuffer(connectionId, chunkIndex, audioData, sequenceNumber = 0) {
    const buffer = this.getAudioBuffer(connectionId);

    if (!buffer.chunks.has(chunkIndex)) {
      buffer.chunks.set(chunkIndex, []);
    }

    buffer.chunks.get(chunkIndex).push({
      data: audioData,
      sequenceNumber,
      timestamp: Date.now(),
    });

    // 更新播放顺序
    if (!buffer.playOrder.includes(chunkIndex)) {
      buffer.playOrder.push(chunkIndex);
      buffer.playOrder.sort((a, b) => a - b); // 确保按顺序播放
    }

    console.log(
      `📦 [音频缓冲] 添加音频 ${connectionId}[${chunkIndex}]: ${audioData.length} bytes`
    );
  }

  /**
   * 获取下一个可播放的音频
   */
  getNextPlayableAudio(connectionId) {
    const buffer = this.getAudioBuffer(connectionId);
    const nextIndex = buffer.lastPlayedIndex + 1;

    if (
      buffer.chunks.has(nextIndex) &&
      buffer.chunks.get(nextIndex).length > 0
    ) {
      const audioArray = buffer.chunks.get(nextIndex);
      const audioData = audioArray.shift(); // 取出第一个音频片段

      // 如果这个chunk的音频都播放完了，清理
      if (audioArray.length === 0) {
        buffer.chunks.delete(nextIndex);
        buffer.lastPlayedIndex = nextIndex;
      }

      return {
        chunkIndex: nextIndex,
        ...audioData,
      };
    }

    return null;
  }

  /**
   * 获取或创建连接池
   */
  getConnectionPool(connectionId) {
    if (!this.connectionPools.has(connectionId)) {
      this.connectionPools.set(connectionId, {
        connections: [],
        activeConnections: 0,
        lastUsed: Date.now(),
      });
    }
    return this.connectionPools.get(connectionId);
  }

  /**
   * 获取可用的连接
   */
  async getAvailableConnection(connectionId) {
    const pool = this.getConnectionPool(connectionId);

    // 查找现有可用连接
    for (const conn of pool.connections) {
      if (
        conn.ws.readyState === WebSocket.OPEN &&
        conn.activeSessions < this.concurrencyConfig.maxConcurrentSessions
      ) {
        return conn;
      }
    }

    // 如果没有可用连接且未达到最大连接数，创建新连接
    if (
      pool.connections.length < this.concurrencyConfig.maxConnectionsPerUser
    ) {
      const newConnection = await this.createPooledConnection(connectionId);
      pool.connections.push(newConnection);
      return newConnection;
    }

    // 等待现有连接可用
    return this.waitForAvailableConnection(connectionId);
  }

  /**
   * 创建池化连接
   */
  async createPooledConnection(connectionId) {
    const connectId = randomUUID();
    const wsHeaders = {
      "X-Api-App-Key": this.appId,
      "X-Api-Access-Key": this.accessToken,
      "X-Api-Resource-Id": this.bidirectionalConfig.resourceId,
      "X-Api-Connect-Id": connectId,
    };

    console.log(
      `🔗 [连接池] 创建新连接 ${connectionId}，Connect-Id: ${connectId}`
    );

    return new Promise((resolve, reject) => {
      const ws = new WebSocket(this.bidirectionalConfig.url, {
        headers: wsHeaders,
        maxPayload: 100 * 1024 * 1024,
      });

      let isResolved = false;

      const connectionInfo = {
        ws,
        connectId,
        activeSessions: 0,
        createdAt: Date.now(),
        lastUsed: Date.now(),
      };

      ws.on("open", async () => {
        try {
          await this.sendStartConnection(ws);
          console.log(`✅ [连接池] 连接已建立 ${connectId}`);
          if (!isResolved) {
            isResolved = true;
            resolve(connectionInfo);
          }
        } catch (error) {
          console.error(`❌ [连接池] 初始化失败 ${connectId}:`, error);
          if (!isResolved) {
            isResolved = true;
            reject(error);
          }
        }
      });

      ws.on("message", (data) => {
        try {
          const response = this.parseResponse(data);
          this.handleBidirectionalResponse(connectionId, response);
        } catch (error) {
          console.error(`❌ [连接池] 解析响应失败 ${connectId}:`, error);
        }
      });

      ws.on("error", (error) => {
        console.error(`❌ [连接池] WebSocket错误 ${connectId}:`, error);
        if (!isResolved) {
          isResolved = true;
          reject(error);
        }
      });

      ws.on("close", () => {
        console.log(`🔌 [连接池] 连接已关闭 ${connectId}`);
        this.removeConnectionFromPool(connectionId, connectId);
      });

      setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          reject(new Error("连接池连接超时"));
        }
      }, 10000);
    });
  }

  /**
   * 等待可用连接
   */
  async waitForAvailableConnection(connectionId, timeout = 5000) {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const checkConnection = () => {
        const pool = this.getConnectionPool(connectionId);

        for (const conn of pool.connections) {
          if (
            conn.ws.readyState === WebSocket.OPEN &&
            conn.activeSessions < this.concurrencyConfig.maxConcurrentSessions
          ) {
            resolve(conn);
            return;
          }
        }

        if (Date.now() - startTime > timeout) {
          reject(new Error("等待可用连接超时"));
          return;
        }

        setTimeout(checkConnection, 100);
      };

      checkConnection();
    });
  }

  /**
   * 从连接池移除连接
   */
  removeConnectionFromPool(connectionId, connectId) {
    const pool = this.connectionPools.get(connectionId);
    if (pool) {
      pool.connections = pool.connections.filter(
        (conn) => conn.connectId !== connectId
      );
      if (pool.connections.length === 0) {
        this.connectionPools.delete(connectionId);
      }
    }
  }

  /**
   * 生成豆包TTS签名
   */
  generateSignature(timestamp, text) {
    const signString = `${this.appId}${timestamp}${text}${this.secretKey}`;
    return crypto.MD5(signString).toString();
  }

  /**
   * 双向流式TTS协议常量
   */
  static get PROTOCOL_CONSTANTS() {
    return {
      // Protocol version and header
      PROTOCOL_VERSION: 0b0001,
      DEFAULT_HEADER_SIZE: 0b0001,

      // Message Types
      FULL_CLIENT_REQUEST: 0b0001,
      AUDIO_ONLY_RESPONSE: 0b1011,
      FULL_SERVER_RESPONSE: 0b1001,
      ERROR_INFORMATION: 0b1111,

      // Message Type Specific Flags
      MSG_TYPE_FLAG_WITH_EVENT: 0b0100,

      // Serialization
      NO_SERIALIZATION: 0b0000,
      JSON: 0b0001,

      // Compression
      COMPRESSION_NO: 0b0000,
      COMPRESSION_GZIP: 0b0001,

      // Events
      EVENT_START_CONNECTION: 1,
      EVENT_FINISH_CONNECTION: 2,
      EVENT_CONNECTION_STARTED: 50,
      EVENT_CONNECTION_FAILED: 51,
      EVENT_CONNECTION_FINISHED: 52,
      EVENT_START_SESSION: 100,
      EVENT_FINISH_SESSION: 102,
      EVENT_SESSION_STARTED: 150,
      EVENT_SESSION_FINISHED: 152,
      EVENT_SESSION_FAILED: 153,
      EVENT_TASK_REQUEST: 200,
      EVENT_TTS_SENTENCE_START: 350,
      EVENT_TTS_SENTENCE_END: 351,
      EVENT_TTS_RESPONSE: 352,
    };
  }

  /**
   * 创建二进制协议头部
   */
  createProtocolHeader(
    messageType,
    messageTypeFlags,
    serialMethod = 0b0001,
    compression = 0b0000
  ) {
    const constants = DoubaoTtsService.PROTOCOL_CONSTANTS;
    return Buffer.from([
      (constants.PROTOCOL_VERSION << 4) | constants.DEFAULT_HEADER_SIZE,
      (messageType << 4) | messageTypeFlags,
      (serialMethod << 4) | compression,
      0x00, // reserved
    ]);
  }

  /**
   * 创建可选字段（事件、会话ID等）
   */
  createOptionalFields(event = null, sessionId = null, connectionId = null) {
    const fields = Buffer.alloc(0);
    let result = Buffer.concat([fields]);

    if (event !== null) {
      const eventBuffer = Buffer.alloc(4);
      eventBuffer.writeInt32BE(event, 0);
      result = Buffer.concat([result, eventBuffer]);
    }

    if (sessionId) {
      const sessionIdBytes = Buffer.from(sessionId, "utf8");
      const sizeBuffer = Buffer.alloc(4);
      sizeBuffer.writeInt32BE(sessionIdBytes.length, 0);
      result = Buffer.concat([result, sizeBuffer, sessionIdBytes]);
    }

    if (connectionId) {
      const connectionIdBytes = Buffer.from(connectionId, "utf8");
      const sizeBuffer = Buffer.alloc(4);
      sizeBuffer.writeInt32BE(connectionIdBytes.length, 0);
      result = Buffer.concat([result, sizeBuffer, connectionIdBytes]);
    }

    return result;
  }

  /**
   * 发送二进制协议消息
   */
  async sendBinaryMessage(
    ws,
    header,
    optional = Buffer.alloc(0),
    payload = Buffer.alloc(0)
  ) {
    const payloadSizeBuffer = Buffer.alloc(4);
    payloadSizeBuffer.writeInt32BE(payload.length, 0);

    const message = Buffer.concat([
      header,
      optional,
      payloadSizeBuffer,
      payload,
    ]);

    if (ws.readyState === WebSocket.OPEN) {
      ws.send(message);
      return true;
    }
    return false;
  }

  /**
   * 解析二进制响应
   */
  parseResponse(buffer) {
    if (buffer.length < 4) {
      throw new Error("Invalid response: too short");
    }

    let offset = 0;

    // 解析头部
    const header = {
      protocolVersion: (buffer[0] >> 4) & 0x0f,
      headerSize: buffer[0] & 0x0f,
      messageType: (buffer[1] >> 4) & 0x0f,
      messageTypeFlags: buffer[1] & 0x0f,
      serialMethod: (buffer[2] >> 4) & 0x0f,
      compression: buffer[2] & 0x0f,
      reserved: buffer[3],
    };

    offset = 4;

    // 解析可选字段
    const optional = {};

    if (header.messageTypeFlags & 0x04) {
      // 包含事件
      if (offset + 4 <= buffer.length) {
        optional.event = buffer.readInt32BE(offset);
        offset += 4;
      }
    }

    // 根据事件类型解析其他字段
    if (
      optional.event ===
      DoubaoTtsService.PROTOCOL_CONSTANTS.EVENT_CONNECTION_STARTED
    ) {
      // 读取连接ID
      if (offset + 4 <= buffer.length) {
        const connectionIdSize = buffer.readInt32BE(offset);
        offset += 4;
        if (offset + connectionIdSize <= buffer.length) {
          optional.connectionId = buffer
            .slice(offset, offset + connectionIdSize)
            .toString("utf8");
          offset += connectionIdSize;
        }
      }
    } else if (
      optional.event === DoubaoTtsService.PROTOCOL_CONSTANTS.EVENT_TTS_RESPONSE
    ) {
      // 读取会话ID
      if (offset + 4 <= buffer.length) {
        const sessionIdSize = buffer.readInt32BE(offset);
        offset += 4;
        if (offset + sessionIdSize <= buffer.length) {
          optional.sessionId = buffer
            .slice(offset, offset + sessionIdSize)
            .toString("utf8");
          offset += sessionIdSize;
        }
      }
    }

    // 读取payload
    let payload = null;
    if (offset + 4 <= buffer.length) {
      const payloadSize = buffer.readInt32BE(offset);
      offset += 4;
      if (offset + payloadSize <= buffer.length) {
        payload = buffer.slice(offset, offset + payloadSize);
      }
    }

    return { header, optional, payload };
  }

  /**
   * 创建双向流式TTS连接
   */
  async createBidirectionalConnection(connectionId) {
    return new Promise((resolve, reject) => {
      const connectId = randomUUID();
      const wsHeaders = {
        "X-Api-App-Key": this.appId,
        "X-Api-Access-Key": this.accessToken,
        "X-Api-Resource-Id": this.bidirectionalConfig.resourceId,
        "X-Api-Connect-Id": connectId,
      };

      console.log(
        `🔗 [双向TTS] 创建连接 ${connectionId}，Connect-Id: ${connectId}`
      );

      const ws = new WebSocket(this.bidirectionalConfig.url, {
        headers: wsHeaders,
        maxPayload: 100 * 1024 * 1024, // 100MB
      });

      let isResolved = false;

      ws.on("open", async () => {
        console.log(`✅ [双向TTS] WebSocket连接已建立 ${connectionId}`);

        try {
          // 发送StartConnection
          await this.sendStartConnection(ws);

          // 存储连接
          this.bidirectionalConnections.set(connectionId, {
            ws,
            connectId,
            status: "connected",
            createdAt: Date.now(),
          });
        } catch (error) {
          console.error(`❌ [双向TTS] 初始化连接失败 ${connectionId}:`, error);
          if (!isResolved) {
            isResolved = true;
            reject(error);
          }
        }
      });

      ws.on("message", (data) => {
        try {
          const response = this.parseResponse(data);
          this.handleBidirectionalResponse(connectionId, response);

          // 如果收到连接成功确认，resolve Promise
          if (
            response.optional.event ===
              DoubaoTtsService.PROTOCOL_CONSTANTS.EVENT_CONNECTION_STARTED &&
            !isResolved
          ) {
            isResolved = true;
            resolve(connectionId);
          }
        } catch (error) {
          console.error(`❌ [双向TTS] 解析响应失败 ${connectionId}:`, error);
        }
      });

      ws.on("error", (error) => {
        console.error(`❌ [双向TTS] WebSocket错误 ${connectionId}:`, error);
        if (!isResolved) {
          isResolved = true;
          reject(error);
        }
      });

      ws.on("close", () => {
        console.log(`🔌 [双向TTS] 连接已关闭 ${connectionId}`);
        this.bidirectionalConnections.delete(connectionId);
        this.activeSessions.delete(connectionId);
      });

      // 超时处理
      setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          reject(new Error("Connection timeout"));
        }
      }, 10000);
    });
  }

  /**
   * 发送StartConnection消息
   */
  async sendStartConnection(ws) {
    const constants = DoubaoTtsService.PROTOCOL_CONSTANTS;
    const header = this.createProtocolHeader(
      constants.FULL_CLIENT_REQUEST,
      constants.MSG_TYPE_FLAG_WITH_EVENT,
      constants.JSON
    );
    const optional = this.createOptionalFields(
      constants.EVENT_START_CONNECTION
    );
    const payload = Buffer.from("{}", "utf8");

    return this.sendBinaryMessage(ws, header, optional, payload);
  }

  /**
   * 发送StartSession消息
   */
  async sendStartSession(ws, sessionId) {
    const constants = DoubaoTtsService.PROTOCOL_CONSTANTS;
    const header = this.createProtocolHeader(
      constants.FULL_CLIENT_REQUEST,
      constants.MSG_TYPE_FLAG_WITH_EVENT,
      constants.JSON
    );
    const optional = this.createOptionalFields(
      constants.EVENT_START_SESSION,
      sessionId
    );

    const sessionMeta = {
      user: { uid: "default_user" },
      req_params: {
        speaker: this.voiceType,
        audio_params: {
          format: this.audioConfig.format,
          sample_rate: this.audioConfig.sample_rate,
        },
      },
    };

    const payload = Buffer.from(JSON.stringify(sessionMeta), "utf8");
    return this.sendBinaryMessage(ws, header, optional, payload);
  }

  /**
   * 发送文本任务请求
   */
  async sendTaskRequest(ws, sessionId, text) {
    const constants = DoubaoTtsService.PROTOCOL_CONSTANTS;
    const header = this.createProtocolHeader(
      constants.FULL_CLIENT_REQUEST,
      constants.MSG_TYPE_FLAG_WITH_EVENT,
      constants.JSON
    );
    const optional = this.createOptionalFields(
      constants.EVENT_TASK_REQUEST,
      sessionId
    );

    const taskData = {
      user: { uid: "default_user" },
      event: constants.EVENT_TASK_REQUEST,
      namespace: "BidirectionalTTS",
      req_params: {
        text: text,
        speaker: this.voiceType,
        audio_params: {
          format: this.audioConfig.format,
          sample_rate: this.audioConfig.sample_rate,
        },
      },
    };

    const payload = Buffer.from(JSON.stringify(taskData), "utf8");
    return this.sendBinaryMessage(ws, header, optional, payload);
  }

  /**
   * 发送FinishSession消息
   */
  async sendFinishSession(ws, sessionId) {
    const constants = DoubaoTtsService.PROTOCOL_CONSTANTS;
    const header = this.createProtocolHeader(
      constants.FULL_CLIENT_REQUEST,
      constants.MSG_TYPE_FLAG_WITH_EVENT,
      constants.JSON
    );
    const optional = this.createOptionalFields(
      constants.EVENT_FINISH_SESSION,
      sessionId
    );
    const payload = Buffer.from("{}", "utf8");

    return this.sendBinaryMessage(ws, header, optional, payload);
  }

  /**
   * 处理双向流式响应
   */
  handleBidirectionalResponse(connectionId, response) {
    const { header, optional, payload } = response;
    const constants = DoubaoTtsService.PROTOCOL_CONSTANTS;

    console.log(`📨 [双向TTS] 收到响应 ${connectionId}:`, {
      messageType: header.messageType,
      event: optional.event,
      payloadSize: payload ? payload.length : 0,
    });

    switch (optional.event) {
      case constants.EVENT_CONNECTION_STARTED:
        console.log(`✅ [双向TTS] 连接已建立 ${connectionId}`);
        break;

      case constants.EVENT_SESSION_STARTED:
        console.log(
          `🎯 [双向TTS] 会话已开始 ${connectionId}, sessionId: ${optional.sessionId}`
        );
        // 更新会话状态
        if (optional.sessionId) {
          const session = this.activeSessions.get(optional.sessionId);
          if (session) {
            session.status = "started";
            session.timestamp = Date.now(); // 更新时间戳
            console.log(
              `✅ [双向TTS] 会话状态已更新: ${optional.sessionId} [chunk ${session.chunkIndex}]`
            );
          } else {
            console.warn(`⚠️ [双向TTS] 未找到会话: ${optional.sessionId}`);
          }
        }
        break;

      case constants.EVENT_TTS_RESPONSE:
        if (header.messageType === constants.AUDIO_ONLY_RESPONSE && payload) {
          // 音频数据响应
          this.handleAudioResponse(connectionId, optional.sessionId, payload);
        }
        break;

      case constants.EVENT_TTS_SENTENCE_START:
        if (payload) {
          try {
            const sentenceInfo = JSON.parse(payload.toString("utf8"));
            console.log(`🎵 [双向TTS] 句子开始 ${connectionId}:`, sentenceInfo);
          } catch (error) {
            console.error(`❌ [双向TTS] 解析句子信息失败:`, error);
          }
        }
        break;

      case constants.EVENT_TTS_SENTENCE_END:
        if (payload) {
          try {
            const sentenceInfo = JSON.parse(payload.toString("utf8"));
            console.log(`✅ [双向TTS] 句子结束 ${connectionId}:`, sentenceInfo);
          } catch (error) {
            console.error(`❌ [双向TTS] 解析句子信息失败:`, error);
          }
        }
        break;

      case constants.EVENT_SESSION_FINISHED:
        console.log(`🏁 [双向TTS] 会话已结束 ${connectionId}`);
        break;

      default:
        console.log(`❓ [双向TTS] 未知事件 ${connectionId}:`, optional.event);
    }
  }

  /**
   * 处理音频响应
   */
  handleAudioResponse(connectionId, sessionId, audioData) {
    console.log(
      `🎵 [双向TTS] 收到音频数据 ${connectionId}, sessionId: ${sessionId}: ${audioData.length} bytes`
    );

    // 获取会话信息
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.warn(`⚠️ [双向TTS] 未找到会话: ${sessionId}`);
      return;
    }

    // 更新会话时间戳
    session.timestamp = Date.now();

    // 获取会话级别的回调
    const callback = this.sessionCallbacks.get(sessionId);
    if (callback) {
      // 调用会话特定的回调函数
      callback({
        type: "audio_chunk",
        data: audioData,
        format: this.audioConfig.format,
        sessionId: sessionId,
        chunkIndex: session.chunkIndex,
        timestamp: Date.now(),
      });
    } else {
      // 如果没有会话回调，添加到音频缓冲区
      this.addAudioToBuffer(connectionId, session.chunkIndex, audioData);
      console.log(`📦 [双向TTS] 音频已缓冲 [chunk ${session.chunkIndex}]`);
    }
  }

  /**
   * 获取或创建TTS队列
   */
  getTtsQueue(connectionId) {
    if (!this.ttsQueues.has(connectionId)) {
      this.ttsQueues.set(connectionId, {
        queue: [],
        processing: false,
        currentCallback: null,
      });
    }
    return this.ttsQueues.get(connectionId);
  }

  /**
   * 文本预处理和分句 - 特别处理数字列表，确保顺序
   */
  preprocessText(text) {
    // 首先移除markdown格式符号
    let cleanText = text
      // 移除粗体标记 **text** 和 __text__
      .replace(/\*\*(.*?)\*\*/g, "$1")
      .replace(/__(.*?)__/g, "$1")
      // 移除斜体标记 *text* 和 _text_
      .replace(/\*(.*?)\*/g, "$1")
      .replace(/_(.*?)_/g, "$1")
      // 移除删除线 ~~text~~
      .replace(/~~(.*?)~~/g, "$1")
      // 移除代码块标记 `code` 和 ```code```
      .replace(/```[\s\S]*?```/g, "")
      .replace(/`([^`]*)`/g, "$1")
      // 移除链接 [text](url)
      .replace(/\[([^\]]*)\]\([^)]*\)/g, "$1")
      // 移除图片 ![alt](url)
      .replace(/!\[[^\]]*\]\([^)]*\)/g, "")
      // 移除标题标记 # ## ### 等
      .replace(/^#{1,6}\s+/gm, "")
      // 移除列表标记前的 - * +
      .replace(/^[\s]*[-\*\+]\s+/gm, "")
      // 移除剩余的单独星号和其他特殊符号
      .replace(/[^\u4e00-\u9fa5\w\s\.,!?;:，。！？；：\-()（）]/g, "")
      .trim();

    // 如果文本长度在限制内，直接返回整个文本
    if (cleanText.length <= this.chunkConfig.maxTextLength) {
      console.log("文本长度适中，不进行分割:", cleanText);
      return [cleanText];
    }

    // 识别数字列表模式，避免在列表项之间分割
    const chunks = [];
    let currentChunk = "";
    let inNumberedList = false;
    let i = 0;

    while (i < cleanText.length) {
      const char = cleanText[i];
      const nextFewChars = cleanText.substring(i, i + 20);

      // 检测数字列表的开始 (如 "1. " 或 "2. " 等) - 移除对星号的依赖
      if (/^\d+\.\s+/.test(nextFewChars)) {
        // 如果当前块不为空且我们遇到了新的列表项
        if (currentChunk.trim().length > 0 && inNumberedList) {
          // 检查是否在合理的分割点
          if (currentChunk.length >= this.chunkConfig.maxTextLength * 0.6) {
            chunks.push(currentChunk.trim());
            currentChunk = "";
          }
        }
        inNumberedList = true;
      }

      currentChunk += char;

      // 只在非列表状态下考虑在句号处分割
      if (!inNumberedList && /[。！？]/.test(char)) {
        if (currentChunk.length >= this.chunkConfig.maxTextLength * 0.5) {
          chunks.push(currentChunk.trim());
          currentChunk = "";
        }
      }
      // 检测列表项的结束 (下一个列表项开始或段落结束)
      else if (inNumberedList && /[。！？]/.test(char)) {
        // 向前看是否有新的列表项
        const remaining = cleanText.substring(i + 1).trim();
        if (!remaining.match(/^\s*\d+\.\s+/)) {
          // 没有下一个列表项，列表结束
          inNumberedList = false;
        }
      }

      // 强制分割过长的文本
      if (currentChunk.length >= this.chunkConfig.maxTextLength) {
        // 在数字列表中，尽量不分割
        if (inNumberedList) {
          // 只有在特别长的情况下才分割
          if (currentChunk.length >= this.chunkConfig.maxTextLength * 1.5) {
            // 寻找段落分割点
            let splitPoint = this.findSafeSplitPoint(currentChunk);
            if (splitPoint > 0) {
              chunks.push(currentChunk.substring(0, splitPoint).trim());
              currentChunk = currentChunk.substring(splitPoint).trim();
            }
          }
        } else {
          // 非列表状态，正常分割
          let splitPoint = this.findSafeSplitPoint(currentChunk);
          if (splitPoint > 0) {
            chunks.push(currentChunk.substring(0, splitPoint).trim());
            currentChunk = currentChunk.substring(splitPoint).trim();
          } else {
            chunks.push(currentChunk.trim());
            currentChunk = "";
          }
        }
      }

      i++;
    }

    // 添加剩余文本
    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim());
    }

    // 过滤空块并确保顺序
    const filteredChunks = chunks.filter((chunk) => chunk.length > 0);

    console.log("文本分割结果（保护数字列表）:");
    filteredChunks.forEach((chunk, index) => {
      console.log(
        `${index + 1}: ${chunk.substring(0, 150)}${
          chunk.length > 150 ? "..." : ""
        }`
      );
    });

    return filteredChunks;
  }

  /**
   * 寻找安全的分割点
   */
  findSafeSplitPoint(text) {
    const maxSearchLength = Math.min(100, text.length);

    // 优先在句号处分割
    for (let i = text.length - 1; i >= text.length - maxSearchLength; i--) {
      if (/[。！？]/.test(text[i])) {
        return i + 1;
      }
    }

    // 其次在逗号处分割
    for (let i = text.length - 1; i >= text.length - maxSearchLength; i--) {
      if (/[，,；;]/.test(text[i])) {
        return i + 1;
      }
    }

    // 最后在空格处分割
    for (let i = text.length - 1; i >= text.length - maxSearchLength; i--) {
      if (/\s/.test(text[i])) {
        return i + 1;
      }
    }

    return 0; // 找不到合适分割点
  }

  /**
   * 调用豆包TTS API
   */
  async callTtsApi(text, retryCount = 0) {
    try {
      const timestamp = Date.now().toString();

      // 使用正确的豆包TTS API格式
      const requestData = {
        app: {
          appid: this.appId,
          token: this.accessToken,
          cluster: "volcano_tts",
        },
        user: {
          uid: "default_user",
        },
        audio: {
          voice_type: this.voiceType,
          encoding: this.audioConfig.format,
          rate: this.audioConfig.sample_rate,
        },
        request: {
          reqid: `tts_${timestamp}`,
          text: text,
          text_type: "plain",
          operation: "query",
        },
      };

      console.log("发送TTS请求:", JSON.stringify(requestData, null, 2));

      const response = await axios.post(this.ttsUrl, requestData, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer;${this.accessToken}`,
        },
        timeout: 30000,
      });

      console.log("TTS响应状态:", response.status);
      console.log("TTS响应数据类型:", typeof response.data);

      if (response.status === 200 && response.data) {
        // 检查返回的数据格式
        if (response.data.code !== undefined) {
          if (response.data.code === 3000) {
            // 成功响应，data字段包含base64编码的音频
            const audioData = Buffer.from(response.data.data, "base64");
            return {
              success: true,
              audioData: audioData,
              format: this.audioConfig.format,
              sequence: response.data.sequence,
              duration: response.data.addition?.duration,
            };
          } else {
            throw new Error(
              `TTS API错误: ${response.data.message} (错误码: ${response.data.code})`
            );
          }
        } else {
          // 直接返回音频数据
          return {
            success: true,
            audioData: response.data,
            format: this.audioConfig.format,
          };
        }
      } else {
        throw new Error(`TTS API响应错误: ${response.status}`);
      }
    } catch (error) {
      console.error("TTS API调用失败:", error.message);

      // 输出详细错误信息用于调试
      if (error.response) {
        console.error("错误响应状态:", error.response.status);
        console.error("错误响应数据:", error.response.data);
        console.error("错误响应头:", error.response.headers);
      }

      // 重试逻辑
      if (retryCount < this.retryConfig.maxRetries) {
        const delay =
          this.retryConfig.retryDelay *
          Math.pow(this.retryConfig.backoffFactor, retryCount);
        console.log(
          `${delay}ms后重试 (${retryCount + 1}/${this.retryConfig.maxRetries})`
        );

        await this.sleep(delay);
        return this.callTtsApi(text, retryCount + 1);
      }

      return {
        success: false,
        error: error.message,
        details: error.response?.data || null,
      };
    }
  }

  /**
   * 音频格式转换
   */
  async convertAudioFormat(audioData, targetFormat = "mp3") {
    try {
      if (targetFormat === "pcm") {
        // 转换为PCM格式 (适配微信小程序)
        const decoded = wav.decode(audioData);
        return {
          success: true,
          audioData: decoded.channelData[0], // 单声道PCM数据
          format: "pcm",
          sampleRate: decoded.sampleRate,
        };
      } else if (targetFormat === "mp3") {
        // MP3格式直接返回
        return {
          success: true,
          audioData: audioData,
          format: "mp3",
        };
      } else {
        throw new Error(`不支持的音频格式: ${targetFormat}`);
      }
    } catch (error) {
      console.error("音频格式转换失败:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 创建音频流分片器
   */
  createAudioChunker(chunkSize = this.chunkConfig.chunkSize) {
    return new Transform({
      transform(chunk, encoding, callback) {
        let offset = 0;
        while (offset < chunk.length) {
          const end = Math.min(offset + chunkSize, chunk.length);
          const audioChunk = chunk.slice(offset, end);
          this.push(audioChunk);
          offset = end;
        }
        callback();
      },
    });
  }

  /**
   * 双向流式TTS处理 - 真正的实时TTS
   */
  async *generateBidirectionalAudioStream(connectionId, text, options = {}) {
    const { format = "mp3", enableChunking = true } = options;

    try {
      console.log(`🚀 [双向TTS] 开始处理文本: "${text.substring(0, 100)}..."`);

      // 确保连接存在
      let connection = this.bidirectionalConnections.get(connectionId);
      if (!connection) {
        console.log(`🔗 [双向TTS] 创建新连接 ${connectionId}`);
        await this.createBidirectionalConnection(connectionId);
        connection = this.bidirectionalConnections.get(connectionId);
      }

      if (!connection || connection.ws.readyState !== WebSocket.OPEN) {
        throw new Error(`双向TTS连接不可用: ${connectionId}`);
      }

      const sessionId = randomUUID().replace(/-/g, "");
      console.log(`🎯 [双向TTS] 创建会话 ${sessionId}`);

      // 开始会话
      await this.sendStartSession(connection.ws, sessionId);

      // 等待会话开始确认
      await this.waitForSessionStart(connectionId, sessionId);

      // 设置音频回调
      const queue = this.getTtsQueue(connectionId);
      const audioChunks = [];
      let isComplete = false;
      let chunkSequence = 1;

      queue.currentCallback = (audioResponse) => {
        if (audioResponse.type === "audio_chunk") {
          audioChunks.push({
            ...audioResponse,
            sequenceNumber: chunkSequence++,
            isLast: false,
          });
        }
      };

      // 发送文本任务
      console.log(`📝 [双向TTS] 发送文本任务: "${text}"`);
      await this.sendTaskRequest(connection.ws, sessionId, text);

      // 立即开始产生音频流
      let yieldedChunks = 0;
      const startTime = Date.now();

      // 使用Promise来处理异步音频接收
      const audioPromise = new Promise((resolve) => {
        const checkAudio = () => {
          if (audioChunks.length > yieldedChunks) {
            return true;
          }
          if (!isComplete) {
            setTimeout(checkAudio, 10); // 10ms检查间隔
          } else {
            resolve();
          }
          return false;
        };
        checkAudio();
      });

      // 流式产生音频数据
      while (!isComplete || audioChunks.length > yieldedChunks) {
        if (audioChunks.length > yieldedChunks) {
          const chunk = audioChunks[yieldedChunks];
          yieldedChunks++;

          // 记录首段音频时间
          if (yieldedChunks === 1) {
            const firstChunkTime = Date.now() - startTime;
            console.log(`🚀 [双向TTS] 首段音频生成时间: ${firstChunkTime}ms`);
          }

          yield {
            type: "audio_chunk",
            data: chunk.data,
            format: chunk.format,
            chunkIndex: 0,
            totalChunks: 1,
            sequenceNumber: chunk.sequenceNumber,
            isLast: chunk.isLast,
            timestamp: chunk.timestamp,
          };
        } else {
          // 等待更多音频数据
          await new Promise((resolve) => setTimeout(resolve, 5));
        }

        // 检查是否应该结束
        if (isComplete && audioChunks.length <= yieldedChunks) {
          break;
        }
      }

      // 结束会话
      await this.sendFinishSession(connection.ws, sessionId);

      // 清理回调
      queue.currentCallback = null;

      const totalTime = Date.now() - startTime;
      console.log(
        `✅ [双向TTS] 处理完成，耗时: ${totalTime}ms，音频片段: ${yieldedChunks}`
      );

      yield {
        type: "complete",
        message: "双向TTS处理完成",
        totalTime: totalTime,
        totalChunks: yieldedChunks,
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error(`❌ [双向TTS] 处理失败:`, error);
      yield {
        type: "error",
        error: error.message,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 等待会话开始确认
   */
  async waitForSessionStart(
    connectionId,
    sessionId,
    chunkIndex,
    timeout = 5000
  ) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      const checkSession = () => {
        const session = this.activeSessions.get(sessionId);
        if (session && session.status === "started") {
          console.log(
            `✅ [双向TTS] 会话启动成功: ${sessionId} [chunk ${chunkIndex}]`
          );
          resolve();
          return;
        }

        if (Date.now() - startTime > timeout) {
          console.error(
            `❌ [双向TTS] 会话启动超时: ${sessionId} [chunk ${chunkIndex}]`
          );
          // 清理失败的会话
          this.activeSessions.delete(sessionId);
          this.sessionCallbacks.delete(sessionId);
          reject(new Error(`等待会话开始超时: ${sessionId}`));
          return;
        }

        setTimeout(checkSession, 50);
      };

      // 设置会话状态监听，包含更多信息
      this.activeSessions.set(sessionId, {
        status: "starting",
        connectionId,
        chunkIndex,
        timestamp: Date.now(),
        startTime: startTime,
      });

      console.log(
        `⏳ [双向TTS] 等待会话启动: ${sessionId} [chunk ${chunkIndex}]`
      );
      checkSession();
    });
  }

  /**
   * 并发TTS处理 - 真正的异步并发合成
   */
  async *generateConcurrentTts(connectionId, text, chunkIndex, options = {}) {
    const { format = "mp3" } = options;

    try {
      console.log(`🚀 [并发TTS] 开始处理chunk [${chunkIndex}]: "${text}"`);

      // 检查并发限制
      const activeSessions = Array.from(this.activeSessions.values()).filter(
        (session) => session.connectionId === connectionId
      );

      if (
        activeSessions.length >= this.concurrencyConfig.maxConcurrentSessions
      ) {
        console.warn(
          `⚠️ [并发TTS] 达到最大并发限制 [${chunkIndex}]: ${activeSessions.length}`
        );
        yield {
          type: "error",
          error: "达到最大并发TTS限制",
          chunkIndex: chunkIndex,
          timestamp: Date.now(),
        };
        return;
      }

      // 获取可用连接
      console.log(`🔗 [并发TTS] 获取可用连接 ${connectionId}`);
      const connection = await this.getAvailableConnection(connectionId);

      if (!connection || connection.ws.readyState !== WebSocket.OPEN) {
        throw new Error(`双向TTS连接不可用: ${connectionId}`);
      }

      // 增加活跃会话计数
      connection.activeSessions++;
      connection.lastUsed = Date.now();

      const sessionId = randomUUID().replace(/-/g, "");
      console.log(`🎯 [并发TTS] 创建会话 ${sessionId} [chunk ${chunkIndex}]`);

      // 设置会话级别的音频回调
      const audioChunks = [];
      let isComplete = false;
      let chunkSequence = 1;

      this.sessionCallbacks.set(sessionId, (audioResponse) => {
        if (audioResponse.type === "audio_chunk") {
          audioChunks.push({
            ...audioResponse,
            sequenceNumber: chunkSequence++,
            isLast: false,
          });
        }
      });

      // 开始会话
      await this.sendStartSession(connection.ws, sessionId);

      // 等待会话开始确认
      await this.waitForSessionStart(connectionId, sessionId, chunkIndex);

      // 发送文本任务
      console.log(`📝 [并发TTS] 发送文本任务: "${text}" [chunk ${chunkIndex}]`);
      await this.sendTaskRequest(connection.ws, sessionId, text);

      // 立即开始产生音频流
      let yieldedChunks = 0;
      const startTime = Date.now();

      // 异步音频处理循环
      while (!isComplete || audioChunks.length > yieldedChunks) {
        if (audioChunks.length > yieldedChunks) {
          const chunk = audioChunks[yieldedChunks];
          yieldedChunks++;

          // 记录首段音频时间
          if (yieldedChunks === 1) {
            const firstChunkTime = Date.now() - startTime;
            console.log(
              `🚀 [并发TTS] 首段音频生成时间 [${chunkIndex}]: ${firstChunkTime}ms`
            );
          }

          yield {
            type: "audio_chunk",
            data: chunk.data,
            format: chunk.format,
            chunkIndex: chunkIndex,
            totalChunks: 1,
            sequenceNumber: chunk.sequenceNumber,
            isLast: chunk.isLast,
            timestamp: chunk.timestamp,
            isConcurrent: true,
          };
        } else {
          // 等待更多音频数据
          await new Promise((resolve) => setTimeout(resolve, 5));
        }

        // 检查会话是否仍然活跃
        const session = this.activeSessions.get(sessionId);
        if (!session) {
          console.warn(
            `⚠️ [并发TTS] 会话已被清理 [${chunkIndex}]: ${sessionId}`
          );
          isComplete = true;
          break;
        }

        // 检查是否应该结束（简单的超时机制）
        if (Date.now() - startTime > 30000) {
          // 30秒超时
          console.warn(`⚠️ [并发TTS] 处理超时 [${chunkIndex}]: ${sessionId}`);
          isComplete = true;
          break;
        }
      }

      // 结束会话
      await this.sendFinishSession(connection.ws, sessionId);

      // 清理回调和会话
      this.sessionCallbacks.delete(sessionId);
      this.activeSessions.delete(sessionId);

      // 减少活跃会话计数
      if (connection.activeSessions > 0) {
        connection.activeSessions--;
      }

      const totalTime = Date.now() - startTime;
      console.log(
        `✅ [并发TTS] 处理完成 [${chunkIndex}]，耗时: ${totalTime}ms，音频片段: ${yieldedChunks}`
      );

      yield {
        type: "complete",
        message: "并发TTS处理完成",
        totalTime: totalTime,
        totalChunks: yieldedChunks,
        chunkIndex: chunkIndex,
        timestamp: Date.now(),
        isConcurrent: true,
      };
    } catch (error) {
      console.error(`❌ [并发TTS] 处理失败 [${chunkIndex}]:`, error);

      // 清理资源
      const sessionEntry = Array.from(this.activeSessions.entries()).find(
        ([_, session]) => session.chunkIndex === chunkIndex
      );
      if (sessionEntry) {
        const [sessionId, session] = sessionEntry;
        this.sessionCallbacks.delete(sessionId);
        this.activeSessions.delete(sessionId);

        // 减少连接的活跃会话计数
        const pool = this.getConnectionPool(session.connectionId);
        for (const conn of pool.connections) {
          if (conn.activeSessions > 0) {
            conn.activeSessions--;
            break;
          }
        }
      }

      yield {
        type: "error",
        error: error.message,
        chunkIndex: chunkIndex,
        timestamp: Date.now(),
        isConcurrent: true,
      };
    }
  }

  /**
   * 立即TTS处理 - 用于AI流式chunk的实时处理（保持向后兼容）
   */
  async *generateImmediateTts(connectionId, text, chunkIndex, options = {}) {
    const { format = "mp3" } = options;

    try {
      console.log(`🚀 [立即TTS] 处理chunk [${chunkIndex}]: "${text}"`);

      // 使用新的并发TTS处理
      const audioStream = this.generateConcurrentTts(
        connectionId,
        text,
        chunkIndex,
        {
          format,
        }
      );

      let audioChunkCount = 0;
      for await (const chunk of audioStream) {
        if (chunk.type === "audio_chunk") {
          audioChunkCount++;
          yield {
            ...chunk,
            isImmediate: true,
            originalText: text,
          };
        } else if (chunk.type === "complete") {
          yield {
            ...chunk,
            isImmediate: true,
            totalAudioChunks: audioChunkCount,
          };
        } else if (chunk.type === "error") {
          yield {
            ...chunk,
            isImmediate: true,
          };
        }
      }
    } catch (error) {
      console.error(`❌ [立即TTS] 处理失败 [${chunkIndex}]:`, error);
      yield {
        type: "error",
        error: error.message,
        chunkIndex: chunkIndex,
        isImmediate: true,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 流式TTS处理 - 绝对按照文本原始顺序（保持向后兼容）
   */
  async *generateAudioStream(text, options = {}) {
    const { format = "mp3", enableChunking = true } = options;

    try {
      // 文本预处理 - 保护数字列表
      const textChunks = this.preprocessText(text);
      console.log(`文本分为${textChunks.length}个片段，严格按原始顺序处理`);

      // 绝对按照数组索引顺序处理，不使用任何异步并发
      for (let i = 0; i < textChunks.length; i++) {
        const chunk = textChunks[i];
        if (chunk.trim().length === 0) continue;

        console.log(
          `[序号${i + 1}] 正在处理: "${chunk.substring(0, 100)}${
            chunk.length > 100 ? "..." : ""
          }"`
        );

        // 同步处理，确保顺序
        const ttsResult = await this.callTtsApi(chunk);

        if (ttsResult.success) {
          const convertResult = await this.convertAudioFormat(
            ttsResult.audioData,
            format
          );

          if (convertResult.success) {
            if (enableChunking) {
              // 直接分割音频数据，不使用Transform流
              const audioData = convertResult.audioData;
              const chunkSize = this.chunkConfig.chunkSize;
              let chunkSequence = 1;

              for (
                let offset = 0;
                offset < audioData.length;
                offset += chunkSize
              ) {
                const end = Math.min(offset + chunkSize, audioData.length);
                const audioChunk = audioData.slice(offset, end);

                yield {
                  type: "audio_chunk",
                  data: audioChunk,
                  format: convertResult.format,
                  chunkIndex: i,
                  totalChunks: textChunks.length,
                  isLast:
                    i === textChunks.length - 1 && end === audioData.length,
                  originalText: chunk,
                  sequenceNumber: chunkSequence++,
                  timestamp: Date.now(),
                };
              }
            } else {
              yield {
                type: "audio_complete",
                data: convertResult.audioData,
                format: convertResult.format,
                chunkIndex: i,
                totalChunks: textChunks.length,
                isLast: i === textChunks.length - 1,
                originalText: chunk,
                sequenceNumber: i + 1,
                timestamp: Date.now(),
              };
            }

            console.log(`[序号${i + 1}] 处理完成`);
          } else {
            yield {
              type: "error",
              error: `音频转换失败: ${convertResult.error}`,
              chunkIndex: i,
              sequenceNumber: i + 1,
            };
          }
        } else {
          yield {
            type: "error",
            error: `TTS生成失败: ${ttsResult.error}`,
            details: ttsResult.details,
            chunkIndex: i,
            sequenceNumber: i + 1,
          };
        }
      }

      yield {
        type: "complete",
        message: "TTS处理完成",
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error("流式TTS处理错误:", error);
      yield {
        type: "error",
        error: error.message,
      };
    }
  }

  /**
   * 简单TTS生成（非流式）
   */
  async generateAudio(text, options = {}) {
    const { format = "mp3" } = options;

    try {
      const ttsResult = await this.callTtsApi(text);

      if (ttsResult.success) {
        const convertResult = await this.convertAudioFormat(
          ttsResult.audioData,
          format
        );
        return convertResult;
      } else {
        return ttsResult;
      }
    } catch (error) {
      console.error("TTS生成错误:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 清理连接资源
   */
  async cleanupConnection(connectionId) {
    console.log(`🧹 [双向TTS] 清理连接资源 ${connectionId}`);

    // 清理连接池
    const pool = this.connectionPools.get(connectionId);
    if (pool) {
      for (const conn of pool.connections) {
        if (conn.ws && conn.ws.readyState === WebSocket.OPEN) {
          conn.ws.close();
        }
      }
      this.connectionPools.delete(connectionId);
    }

    // 关闭旧的单连接（向后兼容）
    const connection = this.bidirectionalConnections.get(connectionId);
    if (connection && connection.ws) {
      if (connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.close();
      }
    }
    this.bidirectionalConnections.delete(connectionId);

    // 清理会话和回调
    const sessionsToDelete = [];
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.connectionId === connectionId) {
        sessionsToDelete.push(sessionId);
      }
    }

    for (const sessionId of sessionsToDelete) {
      this.activeSessions.delete(sessionId);
      this.sessionCallbacks.delete(sessionId);
    }

    // 清理TTS队列
    this.ttsQueues.delete(connectionId);

    // 清理音频缓冲区
    this.audioBuffers.delete(connectionId);

    console.log(
      `🧹 [双向TTS] 已清理连接池和 ${sessionsToDelete.length} 个会话`
    );
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats() {
    const sessionsByConnection = new Map();
    for (const [sessionId, session] of this.activeSessions.entries()) {
      const connectionId = session.connectionId;
      if (!sessionsByConnection.has(connectionId)) {
        sessionsByConnection.set(connectionId, []);
      }
      sessionsByConnection.get(connectionId).push({
        sessionId,
        chunkIndex: session.chunkIndex,
        status: session.status,
        uptime: Date.now() - session.timestamp,
      });
    }

    return {
      activeConnections: this.bidirectionalConnections.size,
      connectionPools: this.connectionPools.size,
      activeSessions: this.activeSessions.size,
      activeCallbacks: this.sessionCallbacks.size,
      activeQueues: this.ttsQueues.size,
      audioBuffers: this.audioBuffers.size,
      concurrencyConfig: this.concurrencyConfig,
      connections: Array.from(this.bidirectionalConnections.entries()).map(
        ([id, conn]) => ({
          id,
          status: conn.status,
          createdAt: conn.createdAt,
          uptime: Date.now() - conn.createdAt,
          sessions: sessionsByConnection.get(id) || [],
        })
      ),
      pools: Array.from(this.connectionPools.entries()).map(([id, pool]) => ({
        id,
        totalConnections: pool.connections.length,
        activeConnections: pool.connections.filter(
          (conn) => conn.ws.readyState === WebSocket.OPEN
        ).length,
        totalActiveSessions: pool.connections.reduce(
          (sum, conn) => sum + conn.activeSessions,
          0
        ),
        lastUsed: pool.lastUsed,
        connections: pool.connections.map((conn) => ({
          connectId: conn.connectId,
          activeSessions: conn.activeSessions,
          status: conn.ws.readyState === WebSocket.OPEN ? "open" : "closed",
          uptime: Date.now() - conn.createdAt,
        })),
      })),
    };
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      service: "DoubaoTTS",
      voiceType: this.voiceType,
      audioConfig: this.audioConfig,
      retryConfig: this.retryConfig,
      chunkConfig: this.chunkConfig,
      bidirectionalConfig: this.bidirectionalConfig,
      connectionStats: this.getConnectionStats(),
    };
  }
}

module.exports = DoubaoTtsService;
