require("dotenv").config();

module.exports = {
  // 服务器配置
  PORT: process.env.PORT || 3000,
  NODE_ENV: process.env.NODE_ENV || "development",

  // 讯飞语音识别配置
  XFYUN: {
    APPID: "1ead2d28",
    API_SECRET: "YTY0ZWE0NWM1ZWJkYWRjYmIwYmJhNjI5",
    API_KEY: "13b210d382b9758d7f2ea54fb999c0cc",
    HOST_URL: "ws://iat-api.xfyun.cn/v2/iat",
  },

  // 豆包TTS配置
  DOUBAO: {
    APPID: "4187083788",
    ACCESS_TOKEN: "NinlxPliwNCENGeSD1O7wh0RaKlRogcG",
    SECRET_KEY: "yl7Xxf1fBgd4gTKVy08SbWXQuYb4DGpg",
    VOICE_TYPE: "zh_female_wanwanxiaohe_moon_bigtts",
    TTS_URL: "https://openspeech.bytedance.com/api/v1/tts",
  },

  // 大模型API配置
  AI: {
    API_URL: "https://fastgptapi.aisheart.cn/v1/chat/completions",
    API_KEY: "TmatJY2wePDu9NX4NNqqgqi4G8IlMfdCJj8coOw6zj0ABlQg",
    MODEL: "Qwen/Qwen2-7B-Instruct",
  },

  // TTS并发控制配置
  TTS_CONCURRENCY: {
    maxConcurrentSessions: 6, // 增加最大并发会话数
    maxConnectionsPerUser: 3, // 增加每用户最大连接数
    sessionTimeout: 10000, // 增加会话超时时间
    cleanupInterval: 20000, // 减少清理间隔
    queueTimeout: 20000, // 增加队列等待超时
    maxQueueSize: 15, // 最大队列大小
    retryAttempts: 2, // 失败重试次数
    retryDelay: 1000, // 重试延迟
  },
};
