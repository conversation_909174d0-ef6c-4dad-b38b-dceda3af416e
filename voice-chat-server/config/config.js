require("dotenv").config();

module.exports = {
  // 服务器配置
  PORT: process.env.PORT || 3000,
  NODE_ENV: process.env.NODE_ENV || "development",

  // 讯飞语音识别配置
  XFYUN: {
    APPID: "1ead2d28",
    API_SECRET: "YTY0ZWE0NWM1ZWJkYWRjYmIwYmJhNjI5",
    API_KEY: "13b210d382b9758d7f2ea54fb999c0cc",
    HOST_URL: "ws://iat-api.xfyun.cn/v2/iat",
  },

  // 豆包TTS配置
  DOUBAO: {
    APPID: "4187083788",
    ACCESS_TOKEN: "NinlxPliwNCENGeSD1O7wh0RaKlRogcG",
    SECRET_KEY: "yl7Xxf1fBgd4gTKVy08SbWXQuYb4DGpg",
    VOICE_TYPE: "zh_female_wanwanxiaohe_moon_bigtts",
    TTS_URL: "https://openspeech.bytedance.com/api/v1/tts",
  },

  // 大模型API配置
  AI: {
    API_URL: "https://fastgptapi.aisheart.cn/v1/chat/completions",
    API_KEY: "TmatJY2wePDu9NX4NNqqgqi4G8IlMfdCJj8coOw6zj0ABlQg",
    MODEL: "Qwen/Qwen2-7B-Instruct",
  },

  // TTS并发控制配置 - 适配豆包TTS 10并发限制
  TTS_CONCURRENCY: {
    // 豆包TTS全局限制
    maxGlobalConnections: 10, // 豆包TTS服务最大并发连接数
    maxConcurrentSessions: 3, // 每连接最大并发会话数
    maxConnectionsPerUser: 2, // 每用户最大连接数（降低以支持更多用户）

    // 超时配置
    sessionTimeout: 8000, // 会话超时时间（缩短以快速释放资源）
    connectionTimeout: 15000, // 连接超时时间
    cleanupInterval: 10000, // 清理间隔（更频繁清理）

    // 队列管理
    queueTimeout: 30000, // 队列等待超时（增加以处理排队）
    maxQueueSize: 50, // 最大队列大小（增加以支持更多排队请求）

    // 重试配置
    retryAttempts: 3, // 失败重试次数
    retryDelay: 1000, // 重试延迟

    // 连接复用策略
    connectionReuseEnabled: true, // 启用连接复用
    idleConnectionTimeout: 30000, // 空闲连接超时
    connectionPriority: "round_robin", // 连接分配策略: round_robin, least_used, priority
  },
};
