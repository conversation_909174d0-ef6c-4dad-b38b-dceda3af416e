#!/usr/bin/env node

/**
 * 双向流式TTS测试脚本
 * 测试新的双向流式TTS功能
 */

const WebSocket = require("ws");

class BidirectionalTtsTest {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.testResults = {
      connectionTime: null,
      firstChunkTime: null,
      totalChunks: 0,
      totalTime: null,
      errors: [],
    };
  }

  async connect() {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      console.log("🔗 连接到WebSocket服务器...");
      this.ws = new WebSocket("ws://localhost:3000");

      this.ws.on("open", () => {
        this.isConnected = true;
        this.testResults.connectionTime = Date.now() - startTime;
        console.log(`✅ 连接成功，耗时: ${this.testResults.connectionTime}ms`);
        resolve();
      });

      this.ws.on("message", (data) => {
        try {
          const message = JSON.parse(data);
          this.handleMessage(message);
        } catch (error) {
          console.error("❌ 解析消息失败:", error);
        }
      });

      this.ws.on("error", (error) => {
        console.error("❌ WebSocket错误:", error);
        this.testResults.errors.push(error.message);
        reject(error);
      });

      this.ws.on("close", () => {
        this.isConnected = false;
        console.log("🔌 连接已关闭");
      });

      // 超时处理
      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error("连接超时"));
        }
      }, 10000);
    });
  }

  handleMessage(message) {
    const timestamp = new Date().toLocaleTimeString();

    switch (message.type) {
      case "connected":
        console.log(
          `[${timestamp}] 📡 服务器确认连接: ${message.connectionId}`
        );
        break;

      case "text_received":
        console.log(`[${timestamp}] 📝 服务器收到文本: ${message.content}`);
        this.testResults.startTime = Date.now();
        break;

      case "ai_start":
        console.log(`[${timestamp}] 🤖 AI开始处理`);
        break;

      case "immediate_tts_start":
        console.log(`[${timestamp}] 🚀 开始立即TTS: ${message.content}`);
        break;

      case "audio_stream":
        this.testResults.totalChunks++;
        if (!this.testResults.firstChunkTime && this.testResults.startTime) {
          this.testResults.firstChunkTime =
            Date.now() - this.testResults.startTime;
          console.log(
            `[${timestamp}] 🎵 首段音频接收，延迟: ${this.testResults.firstChunkTime}ms`
          );
        }

        const isBidirectional = message.metadata?.isBidirectional;
        const chunkInfo = isBidirectional ? "[双向TTS]" : "[传统TTS]";
        console.log(
          `[${timestamp}] 🎵 音频流 ${chunkInfo} [${message.metadata.chunkIndex}]: ${message.data.length} bytes`
        );
        break;

      case "immediate_bidirectional_tts_complete":
        console.log(
          `[${timestamp}] ✅ 双向TTS完成 [${message.chunkIndex}]: ${message.totalTime}ms`
        );
        break;

      case "concurrent_bidirectional_tts_complete":
        console.log(
          `[${timestamp}] 🚀 并发双向TTS完成 [${message.chunkIndex}]: ${message.totalTime}ms`
        );
        if (message.firstChunkTime) {
          console.log(
            `[${timestamp}] 📊 首段音频延迟: ${message.firstChunkTime}ms`
          );
        }
        break;

      case "immediate_tts_complete":
        console.log(
          `[${timestamp}] ✅ 传统TTS完成 [${message.chunkIndex}]: ${message.totalTime}ms`
        );
        break;

      case "ai_complete":
        this.testResults.totalTime = Date.now() - this.testResults.startTime;
        console.log(
          `[${timestamp}] 🎯 AI处理完成，总耗时: ${this.testResults.totalTime}ms`
        );
        this.printTestResults();
        break;

      case "immediate_bidirectional_tts_error":
      case "concurrent_bidirectional_tts_error":
      case "immediate_tts_error":
      case "error":
        let errorType = "TTS";
        if (message.isConcurrent) {
          errorType = "并发双向TTS";
        } else if (message.isBidirectional) {
          errorType = "双向TTS";
        }
        console.error(
          `[${timestamp}] ❌ ${errorType}错误: ${
            message.error || message.message
          }`
        );
        this.testResults.errors.push(message.error || message.message);
        break;

      default:
        console.log(`[${timestamp}] ❓ 未知消息: ${message.type}`);
    }
  }

  async sendTestMessage(text) {
    if (!this.isConnected) {
      throw new Error("WebSocket未连接");
    }

    console.log(`📤 发送测试消息: "${text}"`);

    const message = {
      type: "chat",
      content: text,
      enableTts: true,
      audioFormat: "mp3",
    };

    this.ws.send(JSON.stringify(message));
  }

  printTestResults() {
    console.log("\n📊 测试结果统计:");
    console.log("================");
    console.log(`连接时间: ${this.testResults.connectionTime}ms`);
    console.log(`首段音频延迟: ${this.testResults.firstChunkTime}ms`);
    console.log(`总音频片段: ${this.testResults.totalChunks}`);
    console.log(`总处理时间: ${this.testResults.totalTime}ms`);
    console.log(`错误数量: ${this.testResults.errors.length}`);

    if (this.testResults.errors.length > 0) {
      console.log("\n❌ 错误详情:");
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    // 性能评估
    console.log("\n🎯 性能评估:");
    if (this.testResults.firstChunkTime) {
      if (this.testResults.firstChunkTime < 800) {
        console.log("✅ 首段音频延迟优秀 (<800ms)");
      } else if (this.testResults.firstChunkTime < 1500) {
        console.log("⚠️ 首段音频延迟良好 (<1500ms)");
      } else {
        console.log("❌ 首段音频延迟需要优化 (>1500ms)");
      }
    }

    console.log("\n🔄 测试完成，5秒后关闭连接...");
    setTimeout(() => {
      this.ws.close();
      process.exit(0);
    }, 5000);
  }

  async runTest() {
    try {
      await this.connect();

      // 等待连接稳定
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 发送测试消息
      await this.sendTestMessage(
        "请简单介绍一下《滕王阁序》的创作背景和主要内容"
      );
    } catch (error) {
      console.error("❌ 测试失败:", error);
      process.exit(1);
    }
  }
}

// 运行测试
if (require.main === module) {
  console.log("🚀 开始双向流式TTS测试");
  console.log("确保服务器已启动在 localhost:3000\n");

  const test = new BidirectionalTtsTest();
  test.runTest();
}

module.exports = BidirectionalTtsTest;
